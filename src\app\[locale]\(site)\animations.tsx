"use client";

import { useEffect, useRef } from "react";
import { animate, inView, stagger } from "motion";
import { ElementOrSelector, MarginType } from "@/lib/animation";

type AnimationProps = {
  selector?: ElementOrSelector;
  delay?: number;
  duration?: number;
  staggerFrom?: "first" | "last" | "center" | number;
  staggerDelay?: number;
  inViewProps?: {
    margin?: MarginType;
  };
};

export const useSlideAnimation = <Element extends HTMLElement>(
  props?: AnimationProps & {
    direction?: "left" | "top" | "right" | "bottom";
  },
) => {
  const {
    selector,
    delay = 0,
    duration = 1.5,
    staggerFrom,
    direction = "left",
    inViewProps,
  } = props || {};

  const scope = useRef<Element | null>(null);

  const directionMap = {
    left: {
      clipPath: [
        "polygon(0 0, 0 0, 0 100%, 0 100%)",
        "polygon(100% 0, 0 0, 0 100%, 100% 100%)",
      ],
      x: ["-100%", "0%"],
    },
    top: {
      clipPath: [
        "polygon(0 0, 0 0, 100% 0, 100% 0)",
        "polygon(0 100%, 0 0, 100% 0, 100% 100%)",
      ],
      y: ["-100%", "0%"],
    },
    right: {
      clipPath: [
        "polygon(100% 100%, 100% 100%, 100% 0, 100% 0)",
        "polygon(100% 100%, 0 100%, 0 0, 100% 0)",
      ],
      x: ["100%", "0%"],
    },
    bottom: {
      clipPath: [
        "polygon(100% 100%, 0 100%, 0 100%, 100% 100%)",
        "polygon(100% 100%, 0 100%, 0 0, 100% 0)",
      ],
      y: ["100%", "0%"],
    },
  };

  useEffect(() => {
    if (!scope.current) return;

    const sel =
      typeof selector === "string"
        ? scope.current.querySelectorAll(selector)
        : selector || scope.current;

    const once = inView(
      scope.current,
      () => {
        animate(
          sel,
          {
            opacity: [0, 1],
            ...directionMap[direction],
          },
          {
            ease: "easeOut",
            duration,
            delay: stagger(0.2, { startDelay: delay, from: staggerFrom }),
          },
        );
        return () => {
          once();
        };
      },
      { margin: "-20% 0px", ...inViewProps },
    );

    return () => {
      once();
    };
  }, [scope.current, selector]);

  return { scope };
};

export const useRevel = <Element extends HTMLElement>(
  props?: AnimationProps & {
    direction?: "up" | "center" | "down";
    startPercent?: `${number}%`;
  },
) => {
  const {
    selector,
    delay = 0,
    duration = 1,
    staggerFrom,
    direction = "down",
    startPercent = "-50%",
  } = props || {};

  const scope = useRef<Element | null>(null);

  const directionMap = {
    up: [startPercent, "0%"],
    down: ["50%", "0%"],
    center: undefined,
  };

  useEffect(() => {
    if (!scope.current) return;

    const sel =
      typeof selector === "string"
        ? scope.current.querySelectorAll(selector)
        : selector || scope.current;

    const once = inView(
      scope.current,
      () => {
        animate(
          sel,
          {
            opacity: [0, 1],
            y: directionMap[direction],
            scale: [0.6, 1],
          },
          {
            ease: "easeOut",
            duration,
            delay: stagger(0.2, { startDelay: delay, from: staggerFrom }),
          },
        );

        return () => {
          once();
        };
      },
      { margin: "-20% 0px" },
    );
    return () => {
      once();
    };
  }, [scope.current, selector]);

  return { scope };
};

export const useTextReveal = <Element extends HTMLElement>(
  options?: AnimationProps,
) => {
  const {
    delay = 0,
    duration = 1,
    selector,
    staggerDelay = 0.2,
  } = options || {};

  const scope = useRef<Element | null>(null);

  useEffect(() => {
    if (!scope.current?.textContent) return;
    const text = scope.current.textContent;
    scope.current.textContent = "";

    text.split(" ").forEach((word) => {
      const span = document.createElement("span");
      span.textContent = word + " ";
      span.style.opacity = "0";
      scope.current?.appendChild(span);
    });

    const sel =
      typeof selector === "string"
        ? scope.current.querySelectorAll(selector)
        : selector || scope.current;

    const once = inView(
      scope.current,
      () => {
        scope.current?.classList.remove("opacity-0");
        animate(
          sel,
          {
            opacity: [0, 1],
          },
          {
            ease: "easeOut",
            duration,
            delay: stagger(staggerDelay, { startDelay: delay }),
          },
        );
        return () => {
          once();
        };
      },
      { margin: "-20% 0px" },
    );
  }, [scope.current, selector]);

  return {
    scope,
  };
};

export const useFadeAnimation = <Element extends HTMLElement>(
  props?: AnimationProps & {
    duration?: number;
  },
) => {
  const { selector, delay = 0, duration = 1 } = props || {};

  const scope = useRef<Element | null>(null);

  useEffect(() => {
    if (!scope.current) return;

    const sel =
      typeof selector === "string"
        ? scope.current.querySelectorAll(selector)
        : selector || scope.current;

    const once = inView(
      scope.current,
      () => {
        animate(
          sel,
          {
            opacity: [0, 1],
          },
          {
            ease: "easeOut",
            duration,
            delay,
          },
        );

        return () => {
          once();
        };
      },
      { margin: "-20% 0px" },
    );
  }, [scope.current, selector]);

  return { scope };
};
