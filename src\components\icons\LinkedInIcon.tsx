import { cn } from "@/lib/utils";
import React, { SVGProps } from "react";

type Props = SVGProps<SVGSVGElement> & {
  innerPathClassName?: string;
};

const LinkedInIcon = ({ innerPathClassName = "", ...props }: Props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M18.7528 24.0003H5.2472C2.34938 24.0003 0 21.6511 0 18.7533V5.24743C0 2.34958 2.34938 0.000366211 5.2472 0.000366211H18.7528C21.6506 0.000366211 24 2.34958 24 5.24743V18.7533C24 21.6511 21.6506 24.0003 18.7528 24.0003Z"
      className="fill-current"
    />
    <path
      d="M7.08543 8.96643H3.48535V20.3777H7.08543V8.96643Z"
      className={cn("fill-background", innerPathClassName)}
    />
    <path
      d="M5.3141 3.21313C4.16985 3.21313 3.24219 4.14067 3.24219 5.28484C3.24219 6.429 4.16985 7.35653 5.3141 7.35653C6.45789 7.35653 7.38557 6.429 7.38557 5.28484C7.38557 4.14067 6.45789 3.21313 5.3141 3.21313Z"
      className={cn("fill-background", innerPathClassName)}
    />
    <path
      d="M16.3688 8.62674C13.5839 8.62674 12.7911 10.5287 12.7911 10.5287V8.9664H9.32715V20.3777H12.9272V14.4003C12.9272 13.5852 13.1763 11.7512 15.0326 11.7512C16.8893 11.7512 16.8893 13.9022 16.8893 13.9022V20.3777H20.4439V13.6078C20.4439 10.3928 19.1537 8.62671 16.3688 8.62671V8.62674Z"
      className={cn("fill-background", innerPathClassName)}
    />
  </svg>
);

export default LinkedInIcon;
