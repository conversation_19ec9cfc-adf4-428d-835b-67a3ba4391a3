"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import { useLenis } from "@/lib/lenis";
import { cn } from "@/lib/utils";
import { marked } from "marked";
import { useEffect, useState } from "react";
import { LinkType } from "../links";
import { useTranslations } from "next-intl";
import { useRevel } from "../animations";

const InfoCarousel = ({
  className,
  infosList,
}: {
  className?: string;
  infosList: LinkType[];
}) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const lenis = useLenis();
  const { scope } = useRevel<HTMLHeadingElement>();

  const t = useTranslations("Common");

  const handlePageChange = (index: number) => {
    if (!api) return;
    api.scrollTo(index);
  };

  const handleKnowMore = () => {
    if (!lenis) return;
    lenis.scrollTo(infosList[current - 1].href, {
      offset: -100,
    });
  };

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <section
      ref={scope}
      className={cn(
        "relative flex w-full flex-col items-center justify-center gap-8 text-center opacity-0 sm:max-w-3xl",
        className,
      )}
    >
      <Carousel setApi={setApi} className="grid w-full">
        <CarouselContent>
          {infosList.map(({ description, label }) => (
            <CarouselItem key={label}>
              <article className="flex h-full w-full flex-col items-center justify-center gap-4 text-center">
                <h3 className="relative text-xl font-light lowercase text-background opacity-80 before:absolute before:left-[-110%] before:top-1/2 before:h-0.25 before:w-full before:-translate-y-1/2 before:bg-background after:absolute after:left-[110%] after:top-1/2 after:h-0.25 after:w-full after:-translate-y-1/2 after:bg-background lg:text-2xl">
                  {label}
                </h3>
                <h2
                  className="line-clamp-10 max-w-xl text-balance text-2xl font-bold text-background lg:text-4xl lg:text-accent-foreground"
                  dangerouslySetInnerHTML={{
                    __html: marked.parse(description),
                  }}
                ></h2>
              </article>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
      <span className="flex justify-center gap-2">
        {[...Array(count)].map((_, index) => (
          <button
            onClick={() => handlePageChange(index)}
            key={index}
            className={cn(
              "size-2.5 rounded-full",
              current === index + 1 ? "bg-background" : "bg-muted-foreground",
            )}
          />
        ))}
      </span>
      <Button variant="outline" className="lowercase" onClick={handleKnowMore}>
        {t("knowMore")}
      </Button>
    </section>
  );
};

export default InfoCarousel;
