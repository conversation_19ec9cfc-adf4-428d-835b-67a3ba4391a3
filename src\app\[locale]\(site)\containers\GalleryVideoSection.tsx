"use client";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useSlideAnimation } from "../animations";
import VideoGrid, { VideoGridItem } from "../gallery/VideoGrid";
import { useTranslations } from "next-intl";

const GalleryVideoSection = ({
  expanded,
  onExpand,
}: {
  expanded: boolean;
  onExpand: () => void;
}) => {
  const { scope: textScope } = useSlideAnimation<HTMLParagraphElement>({
    direction: "left",
  });

  const { scope: buttonRef } = useSlideAnimation<HTMLButtonElement>({
    direction: "bottom",
    delay: 0.5,
  });

  const t = useTranslations("Gallery");

  return (
    <section id="video-gallery">
      <div className="flex px-5 max-sm:flex-col sm:px-10">
        <article className="flex flex-1 flex-col overflow-hidden bg-foreground">
          <div className="m-auto flex flex-col justify-center gap-8 max-lg:py-8">
            <p
              ref={textScope}
              className={cn(
                "font-bold opacity-0 transition-all duration-300",
                expanded ? "text-2xl sm:text-4xl" : "text-lg sm:text-2xl",
              )}
            >
              {t.rich("videoSubtitle", {
                line: (chunks) => (
                  <>
                    {chunks}
                    <br />
                  </>
                ),
              })}
            </p>
            <Button
              ref={buttonRef}
              variant="outline"
              className={cn(
                "w-fit transform-gpu border-secondary-foreground opacity-0 transition-all duration-300",
              )}
              onClick={onExpand}
            >
              {expanded ? t("close") : t("explore")}
            </Button>
          </div>
        </article>
        <VideoGridItem
          className="aspect-[4/2.5] flex-1 sm:max-w-[50%]"
          expanded={expanded}
        />
      </div>
      <VideoGrid expanded={expanded} />
    </section>
  );
};

export default GalleryVideoSection;
