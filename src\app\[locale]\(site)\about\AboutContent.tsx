import { Button } from "@/components/ui/button";
import { use<PERSON>enis } from "@/lib/lenis";
import { cn } from "@/lib/utils";
import { useFormatter, useTranslations } from "next-intl";
// import { aboutText } from "./data";

type Props = {
  className?: string;
  isExpanded: boolean;
  toggleExpand: () => void;
  ref?: React.RefObject<HTMLElement | null>;
};

const AboutContent = ({ ref, isExpanded, toggleExpand, className }: Props) => {
  const lenis = useLenis();
  const format = useFormatter();
  const t = useTranslations("About");

  const handleToggleExpand = () => {
    if (!lenis) return;
    lenis.scrollTo("#about-content", {
      offset: -200,
    });
    toggleExpand();
  };

  return (
    <section
      ref={ref}
      id="about-content"
      className={cn(
        "relative flex w-full scroll-mt-40 flex-col",
        className,
        isExpanded ? "gap-10" : "gap-0",
      )}
    >
      <div
        className={cn(
          "grid h-auto transition-all duration-500 ease-in-out",
          isExpanded ? "grid-rows-[1fr]" : "grid-rows-[0fr]",
        )}
      >
        <section
          data-lenis-prevent
          className={cn(
            "h-full max-h-[85vh] rounded-3xl bg-white/80 shadow-lg transition-all duration-300 ease-in-out",
            isExpanded ? "overflow-y-auto" : "overflow-hidden",
          )}
        >
          <div
            data-lenis-prevent
            className="flex flex-col gap-10 overflow-y-auto overscroll-contain p-10"
          >
            <div className="flex flex-col gap-4 [&_li]:font-medium [&_ol]:list-inside [&_ol]:list-[auto] [&_p]:font-medium [&_ul>li]:list-inside [&_ul>li]:marker:text-muted [&_ul]:list-disc">
              {t.rich("biography", {
                paragraph: (chunk) => <p>{chunk}</p>,
              })}
            </div>
          </div>
        </section>
      </div>
      <Button
        onClick={handleToggleExpand}
        variant="outline"
        className="mx-auto mt-10 w-fit"
      >
        {isExpanded ? "Fechar" : "Explorar"}
      </Button>
    </section>
  );
};

export default AboutContent;
