import { type SchemaTypeDefinition } from "sanity";

import { blockContentType } from "./blockContentType";
import { newsletterType } from "./newsletterType";
import { newsletterStatsType } from "./newsletterStatsType";
import { subscriberType } from "./subscriberType";

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    blockContentType,
    newsletterType,
    newsletterStatsType,
    subscriberType,
  ],
};
