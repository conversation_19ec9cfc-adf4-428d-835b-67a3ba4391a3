"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useLenis } from "@/lib/lenis";
import { cn } from "@/lib/utils";
import { useRef } from "react";
import { useSlideAnimation } from "../animations";
import { sculptureImages } from "../data-new";
import ImageGrid, { ImageGridItem } from "../gallery/ImageGrid";
import { useTranslations } from "next-intl";

const GallerySculptureSection = ({
  isExpanded: expanded,
  onExpand,
}: {
  isExpanded: boolean;
  onExpand: () => void;
}) => {
  const lenis = useLenis();
  const timeoutRef = useRef<number | null>(null);

  const { scope: textScope } = useSlideAnimation<HTMLParagraphElement>({
    direction: "right",
  });

  const { scope: buttonRef } = useSlideAnimation<HTMLButtonElement>({
    direction: "bottom",
    delay: 0.5,
  });

  const t = useTranslations("Gallery");

  return (
    <section id="sculpture-gallery">
      <div className="flex px-5 max-sm:flex-col-reverse sm:px-10">
        <ImageGridItem
          className="aspect-[4/2.5] sm:max-w-[50%]"
          image={sculptureImages[0]}
          images={sculptureImages.slice(0, 3)}
          isExpanded={expanded}
        />
        <article className="relative flex flex-1 flex-col items-center justify-center text-end max-lg:py-8">
          <div className="relative m-auto flex flex-col justify-center gap-8">
            <p
              ref={textScope}
              className={cn(
                "font-bold opacity-0 transition-all duration-300",
                expanded ? "text-2xl sm:text-4xl" : "text-lg sm:text-2xl",
              )}
            >
              {t.rich("sculptureSubtitle", {
                line: (chunks) => (
                  <>
                    {chunks}
                    <br />
                  </>
                ),
              })}
            </p>
            <Button
              ref={buttonRef}
              onClick={() => {
                onExpand();
                if (timeoutRef.current) clearTimeout(timeoutRef.current);
                timeoutRef.current = window.setTimeout(() => {
                  lenis?.scrollTo("#sculpture-gallery", {
                    offset: -100,
                  });
                }, 500);
              }}
              variant="outline"
              className={cn(
                "ml-auto w-fit border-background text-background opacity-0",
              )}
            >
              {expanded ? t("close") : t("explore")}
            </Button>
          </div>
        </article>
      </div>
      <ImageGrid images={sculptureImages.slice(1, sculptureImages.length)} isExpanded={expanded} />
    </section>
  );
};

export default GallerySculptureSection;
