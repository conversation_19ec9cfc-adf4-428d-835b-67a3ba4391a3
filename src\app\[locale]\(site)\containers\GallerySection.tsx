"use client";
import { useEffect, useState } from "react";
import { linksObj } from "../links";
import PhotoSwipeLightbox from "photoswipe/lightbox";
import "photoswipe/style.css";
import GalleryArtSection from "./GalleryArtSection";
import GallerySculptureSection from "./GallerySculptureSection";
import GalleryVideoSection from "./GalleryVideoSection";

const GallerySection = () => {
  const [expanded, setExpanded] = useState<"art" | "sculpture" | "video" | "none">(
    "none",
  );
  useEffect(() => {
    let lightbox: PhotoSwipeLightbox | null = new PhotoSwipeLightbox({
      gallery: `${linksObj.gallery.href} a`,
      pswpModule: () => import("photoswipe"),
    });
    lightbox.init();

    return () => {
      lightbox?.destroy();
      lightbox = null;
    };
  }, [expanded]);

  const handleExpand = (expand: "art" | "sculpture" | "video") => {
    setExpanded((prev) => (prev === expand ? "none" : expand));
  };

  return (
    <section
      id={linksObj.gallery.href.replace("#", "")}
      className="relative flex flex-col bg-black/70 text-white"
    >
      <GalleryArtSection
        isExpanded={expanded === "art"}
        onExpand={() => handleExpand("art")}
      />
      <GallerySculptureSection
        isExpanded={expanded === "sculpture"}
        onExpand={() => handleExpand("sculpture")}
      />
      <GalleryVideoSection
        expanded={expanded === "video"}
        onExpand={() => handleExpand("video")}
      />
    </section>
  );
};

export default GallerySection;
