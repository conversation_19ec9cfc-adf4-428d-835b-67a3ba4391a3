"use client";

import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/date";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useState } from "react";
import { useSlideAnimation } from "../animations";
import { linksObj } from "../links";
import { pressData } from "./data";

const PressSection = ({ className }: { className?: string }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const t = useTranslations("Press");

  const { scope: titleRef } = useSlideAnimation<HTMLHeadingElement>({
    selector: "span",
  });
  
  const { scope: itemScope } = useSlideAnimation<HTMLElement>({
    direction: "bottom",
    selector: "a",
    delay: 1,
  });

  const { scope: buttonRef } = useSlideAnimation<HTMLButtonElement>({
    direction: "bottom",
    delay: 1,
  });

  const displayedNews = isExpanded ? pressData : pressData.slice(0, 2);

  return (
    <section
      id={linksObj.press.href.replace("#", "")}
      className={cn(
        "flex flex-col gap-8 overflow-y-hidden bg-background",
        className,
      )}
    >
      <h1
        ref={titleRef}
        className="mb-8 text-3xl font-bold lowercase lg:text-6xl"
      >
        <span className="inline-block opacity-0">{t("title")}</span>
      </h1>

      <nav
        ref={itemScope}
        rel="noreferrer"
        className="grid grid-cols-1 gap-10 sm:grid-cols-2"
      >
        {displayedNews.map((item, index) => (
          <a
            href={"#" + item.title.toLowerCase().replaceAll(" ", "-")}
            target="_blank"
            key={item.title + index}
            className="group grid gap-5 lg:grid-cols-2 lg:even:ml-10"
          >
            <Image
              src={item.images[0].src}
              width={500}
              height={500}
              alt={t("altText")}
              className="h-full max-h-63 w-full max-w-126 rounded-xl object-cover"
            />
            <div className="flex flex-col gap-2">
              <h2 className="text-lg font-semibold text-foreground group-hover:text-primary group-focus-visible:text-primary sm:text-xl">
                {item.title}
              </h2>
              {item.type !== "news" && (
                <p className="line-clamp-4 text-sm sm:text-base">
                  {item.description}
                </p>
              )}
              <p className="text-xs font-medium text-muted-foreground sm:text-sm">
                {item.font} | {formatDate({ date: item.date })}
              </p>
            </div>
          </a>
        ))}
      </nav>

      <Button
        ref={buttonRef}
        variant="outline"
        className="mx-auto mt-8 opacity-0"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? t("close") : t("archives")}
      </Button>
    </section>
  );
};

export default PressSection;
