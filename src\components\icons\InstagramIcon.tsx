import { cn } from "@/lib/utils";
import React, { SVGProps } from "react";

type Props = SVGProps<SVGSVGElement> & {
  innerPathClassName?: string;
};

const InstagramIcon = ({ innerPathClassName = "", ...props }: Props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M8.09553 5.1268C6.17477 5.1268 4.6124 6.68917 4.6124 8.60957V16.4086C4.6124 18.329 6.17477 19.8914 8.09553 19.8914H15.8946C17.8149 19.8914 19.3773 18.329 19.3773 16.4086V8.60957C19.3773 6.68917 17.8149 5.1268 15.8946 5.1268H8.09553ZM15.8946 21.7509H8.09553C5.14953 21.7509 2.75293 19.3543 2.75293 16.4086V8.60953C2.75293 5.66377 5.14953 3.26721 8.09553 3.26721H15.8946C18.8401 3.26721 21.2367 5.66377 21.2367 8.60953V16.4086C21.2367 19.3543 18.8401 21.7509 15.8946 21.7509Z"
      className={cn("fill-background", innerPathClassName)}
    />
    <path
      d="M11.9955 9.38492C10.2728 9.38492 8.87126 10.7864 8.87126 12.5091C8.87126 14.2318 10.2728 15.6333 11.9955 15.6333C13.7182 15.6333 15.1193 14.2318 15.1193 12.5091C15.1193 10.7864 13.7182 9.38492 11.9955 9.38492ZM11.9955 17.1871C9.41595 17.1871 7.31738 15.0886 7.31738 12.5091C7.31738 9.92961 9.41595 7.83105 11.9955 7.83105C14.5746 7.83105 16.6732 9.92961 16.6732 12.5091C16.6732 15.0886 14.5746 17.1871 11.9955 17.1871Z"
      className={cn("fill-background", innerPathClassName)}
    />
    <path
      d="M17.8554 7.59885C17.8554 8.20101 17.3673 8.68915 16.7651 8.68915C16.1629 8.68915 15.6748 8.20101 15.6748 7.59885C15.6748 6.99667 16.1629 6.50854 16.7651 6.50854C17.3673 6.50854 17.8554 6.99667 17.8554 7.59885Z"
      className={cn("fill-background", innerPathClassName)}
    />
    <path
      d="M18.7013 24.0003H5.29875C2.37209 24.0003 0 21.6279 0 18.7015V5.29875C0 2.37237 2.37209 0 5.29875 0H18.7013C21.6279 0 24 2.37237 24 5.29875V18.7015C24 21.6279 21.6279 24.0003 18.7013 24.0003Z"
      className="fill-current"
    />
    <path
      d="M8.13377 4.68086C6.22951 4.68086 4.68034 6.22992 4.68034 8.1339V15.8664C4.68034 17.7704 6.22951 19.3195 8.13377 19.3195H15.8659C17.7702 19.3195 19.3193 17.7704 19.3193 15.8664V8.1339C19.3193 6.22992 17.7702 4.68086 15.8659 4.68086H8.13377ZM15.8659 21.1631H8.13377C5.21277 21.1631 2.83691 18.787 2.83691 15.8664V8.13387C2.83691 5.21325 5.21277 2.83716 8.13377 2.83716H15.8659C18.7869 2.83716 21.1627 5.21325 21.1627 8.13387V15.8664C21.1627 18.787 18.7869 21.1631 15.8659 21.1631Z"
      className={cn("fill-background", innerPathClassName)}
    />
    <path
      d="M12.0004 8.90263C10.2923 8.90263 8.90299 10.2922 8.90299 12.0002C8.90299 13.7081 10.2923 15.0977 12.0004 15.0977C13.7084 15.0977 15.0977 13.7081 15.0977 12.0002C15.0977 10.2922 13.7084 8.90263 12.0004 8.90263ZM12.0004 16.6383C9.44295 16.6383 7.3623 14.5577 7.3623 12.0002C7.3623 9.44271 9.44295 7.36206 12.0004 7.36206C14.5578 7.36206 16.6384 9.44271 16.6384 12.0002C16.6384 14.5577 14.5578 16.6383 12.0004 16.6383Z"
      className={cn("fill-background", innerPathClassName)}
    />
    <path
      d="M17.8102 7.13179C17.8102 7.72882 17.3263 8.21281 16.7293 8.21281C16.1323 8.21281 15.6484 7.72882 15.6484 7.13179C15.6484 6.53476 16.1323 6.05078 16.7293 6.05078C17.3263 6.05078 17.8102 6.53476 17.8102 7.13179Z"
      className={cn("fill-background", innerPathClassName)}
    />
  </svg>
);

export default InstagramIcon;
