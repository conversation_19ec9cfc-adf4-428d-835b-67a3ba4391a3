"use client";
import LogoImage from "@/assets/imgs/signature.svg";
import Image from "next/image";
import { Link } from "@/i18n/navigation";
import LangDropdown from "./LangDropdown";
import NavLinks from "./NavLinks";
import SocialButtons from "./SocialButtons";
import { cn } from "@/lib/utils";
import NavMenu from "./NavMenu";
import { getLinks } from "../links";
import { useSlideAnimation } from "../animations";
import { useTranslations } from "next-intl";
import { useLenis } from "@/lib/lenis";
import { useState, useDeferredValue } from "react";

const Header = () => {
  const t = useTranslations("Links");
  const links = Object.values(getLinks(t));

  const { scope: logoScope } = useSlideAnimation<HTMLAnchorElement>({
    selector: "img",
    inViewProps: { margin: "10% 0px" },
  });

  const [threshold, setThreshold] = useState(false);
  const deferredThreshold = useDeferredValue(threshold);

  useLenis((lenis) => {
    lenis.progress > 0.2 ? setThreshold(true) : setThreshold(false);
  }, []);

  return (
    <header className="sticky top-0 z-40 w-full">
      <div className="absolute top-[30%] left-[50%] z-[-1] h-[50%] w-full translate-x-[-50%] rounded-[50%] bg-green-300 [box-shadow:0px_20px_15px_20px_rgba(0,0,0,.7)]"></div>
      <div className="bg-background">
        <section className="mx-auto flex w-full max-w-360 items-center justify-between p-5 py-3 sm:px-20">
          <Link href="/" ref={logoScope}>
            <Image
              src={LogoImage}
              alt="logo"
              width={150}
              height={50}
              className={cn(
                "inline-block h-auto object-contain opacity-0 transition-all duration-300 ease-out",
                deferredThreshold ? "w-28" : "w-36",
              )}
              unoptimized
            />
          </Link>
          <div
            className={cn(
              "flex max-w-96 flex-wrap items-center justify-end gap-x-6 overflow-hidden transition-all duration-300 ease-out",
              deferredThreshold ? "gap-y-0" : "gap-y-2",
            )}
          >
            <span
              className={cn(
                "grid grid-flow-col gap-x-6 transition-all duration-500 ease-in-out",
                deferredThreshold
                  ? "grid-rows-[0fr] [&>button]:py-0"
                  : "grid-rows-[1fr]",
              )}
            >
              <SocialButtons className="overflow-hidden max-sm:hidden" />
              <LangDropdown className="overflow-hidden" />
            </span>
            <NavLinks className="max-sm:hidden" links={links} />
            <NavMenu className="sm:hidden" />
          </div>
        </section>
      </div>
    </header>
  );
};

export default Header;
