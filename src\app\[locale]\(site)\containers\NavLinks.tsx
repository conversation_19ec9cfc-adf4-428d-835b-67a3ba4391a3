"use client";
import { Link } from "@/i18n/navigation";
import { LinkType } from "../links";
import { cn } from "@/lib/utils";
import { lenisScrollTo, useLenis } from "@/lib/lenis";
import { useSlideAnimation } from "../animations";

const NavLinks = ({
  className,
  links,
}: {
  className?: string;
  links: LinkType[];
}) => {
  const lenis = useLenis();
  const { scope } = useSlideAnimation({
    direction: "bottom",
    selector: "a",
    inViewProps: { margin: "10% 0px" },
    delay: 1.5,
    duration: 0.5,
  });

  return (
    <nav
      ref={scope}
      className={cn(
        "flex items-center gap-4 transition-all duration-300 ease-out",
        className,
      )}
    >
      {links.map(({ href, label }) => {
        return (
          <Link
            onClick={() => lenisScrollTo({ href, lenis })}
            key={href}
            href={href}
            className="text-foreground first-of-type:text-primary-foreground hover:text-primary font-medium lowercase opacity-0"
          >
            {label}
          </Link>
        );
      })}
    </nav>
  );
};

export default NavLinks;
