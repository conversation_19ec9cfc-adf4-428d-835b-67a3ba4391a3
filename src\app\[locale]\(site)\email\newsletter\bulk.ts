import { sendNewsletterEmail } from "./newsletter";
import { sendSupportNotification } from "./support";
import type { Newsletter, Subscriber } from "@/sanity/sanity.types";

export async function sendEmailsToSubscribers(
  newsletter: Newsletter,
  subscribers: Subscriber[],
): Promise<{ sentCount: number; failedEmails: string[]; stopped?: boolean }> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://pedroyaba.com";
  let sentCount = 0;
  const failedEmails: string[] = [];
  const totalSubscribers = subscribers.length;
  const failureThreshold = Math.ceil(totalSubscribers * 0.1); // 10% threshold

  console.log(
    `📧 Sending newsletter "${newsletter.subject}" to ${totalSubscribers} subscribers...`,
  );
  console.log(`⚠️ Will stop if ${failureThreshold} or more emails fail`);

  for (const subscriber of subscribers) {
    try {
      const unsubscribeUrl = `${baseUrl}/pt/unsubscribe?token=${subscriber.unsubscribeToken}`;
      await sendNewsletterEmail(subscriber.email, newsletter, unsubscribeUrl);
      sentCount++;
      console.log(`✅ Newsletter sent to: ${subscriber.email}`);
    } catch (error) {
      console.error(
        `❌ Failed to send newsletter to ${subscriber.email}:`,
        error,
      );
      failedEmails.push(subscriber.email);

      // Check if we've reached the failure threshold
      if (failedEmails.length >= failureThreshold) {
        console.error(
          `🚨 CRITICAL: ${failedEmails.length}/${totalSubscribers} emails failed (${Math.round((failedEmails.length / totalSubscribers) * 100)}%). Stopping execution.`,
        );

        // Send notification to support
        await sendSupportNotification(newsletter, {
          totalSubscribers,
          sentCount,
          failedCount: failedEmails.length,
          failedEmails,
          stoppedEarly: true,
        });

        return { sentCount, failedEmails, stopped: true };
      }
    }
  }

  return { sentCount, failedEmails, stopped: false };
}

/**
 * Send support notification for completed newsletters with high failure rates
 */
export async function notifyIfHighFailureRate(
  newsletter: Newsletter,
  totalSubscribers: number,
  sentCount: number,
  failedEmails: string[],
): Promise<void> {
  if (failedEmails.length === 0) return;

  const failureRate = Math.round(
    (failedEmails.length / totalSubscribers) * 100,
  );

  // Notify support if failure rate is high but below stopping threshold (5-9%)
  if (failureRate >= 5) {
    await sendSupportNotification(newsletter, {
      totalSubscribers,
      sentCount,
      failedCount: failedEmails.length,
      failedEmails,
      stoppedEarly: false,
    });
  }
}
