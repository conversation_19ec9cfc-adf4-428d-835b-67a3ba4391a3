import { StaticImageData } from "next/image";
import Image2 from "@/assets/imgs/_09A2156.jpg";

const categories = {
  all: "Todos",
  frame: "Quadro",
  sculpture: "Escultura",
  custom: "Personalizado",
} as const;

type ImageItem = {
  title: string;
  src: string | StaticImageData;
  width: number;
  height: number;
  category: keyof typeof categories;
};

/** @deprecated use instead the data from `./data-new.ts` */
export const galleryImages: ImageItem[] = [
  {
    title: "<PERSON><PERSON> Fiesta",
    src: "/imgs/_09A2155.jpg",
    width: 3566,
    height: 2853,
    category: "frame",
  },
  {
    title: "La Reine des Echec",
    src: Image2,
    width: 3983,
    height: 3186,
    category: "sculpture",
  },
  {
    title: "Mussulo Beach",
    src: "/imgs/_09A2158.jpg",
    width: 4386,
    height: 3509,
    category: "custom",
  },
  {
    title: "La Baillarine",
    src: "/imgs/_09A2159.jpg",
    width: 4252,
    height: 3402,
    category: "frame",
  },
  {
    title: "Baia Farta",
    src: "/imgs/_09A2161.jpg",
    width: 4419,
    height: 6626,
    category: "sculpture",
  },
  {
    title: "Porto Vecchio",
    src: "/imgs/_09A2162.jpg",
    width: 5293,
    height: 4243,
    category: "custom",
  },
  {
    title: "La Terrazza",
    src: "/imgs/_09A2163.jpg",
    width: 6038,
    height: 4817,
    category: "frame",
  },
  {
    title: "Divina Fiesta 2",
    src: "/imgs/_09A2164.jpg",
    width: 4831,
    height: 3831,
    category: "sculpture",
  },
  {
    title: "La Reine des Echec 2",
    src: "/imgs/_09A2165.jpg",
    width: 6600,
    height: 4070,
    category: "custom",
  },
];
