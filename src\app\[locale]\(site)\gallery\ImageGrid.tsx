import { cn } from "@/lib/utils";
import Image, { getImageProps } from "next/image";
import { Link } from "@/i18n/navigation";
import "photoswipe/style.css";
import { useState, useEffect, useRef, useDeferredValue, useMemo } from "react";
import { animate, inView } from "motion";
import { AnimationPlaybackControls } from "@/lib/animation";
import { ImageItem } from "../data-new";

type ImageGridProps = {
  className?: string;
  isExpanded?: boolean;
  images: ImageItem[];
};

export const ImageGridItem = ({
  image,
  images,
  className,
  isExpanded,
}: {
  isExpanded: boolean;
  className?: string;
  image: ImageItem;
  images?: ImageItem[];
}) => {
  const {
    props: { src, height, width, srcSet },
  } = getImageProps({
    width: image.width,
    height: image.height,
    src: image.src,
    alt: image.title,
    sizes: "50vw",
  });

  const normalMode = !images || isExpanded;

  const figRef = useRef<HTMLElement>(null);
  const intervalId = useRef<NodeJS.Timeout | undefined>(undefined);
  const controlRef = useRef<AnimationPlaybackControls | undefined>(undefined);

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const deferredCurrentImageIndex = useDeferredValue(currentImageIndex);

  const [prevDisplayedImage, displayedImage] = useMemo(() => {
    if (normalMode) return [undefined, image];

    const prevImage =
      deferredCurrentImageIndex <= 0
        ? images[images.length - 1]
        : images[deferredCurrentImageIndex - 1];

    const currentImage = images[deferredCurrentImageIndex];

    return [prevImage, currentImage];
  }, [images, isExpanded, deferredCurrentImageIndex]);

  const startAnimation = () => {
    clearInterval(intervalId.current);
    intervalId.current = setInterval(() => {
      if (normalMode) return;
      setCurrentImageIndex((prevIndex: number) =>
        prevIndex === images.length - 1 ? 0 : prevIndex + 1,
      );
    }, 5000);
  };

  const handleNextImage = () => {
    if (normalMode) return;
    clearInterval(intervalId.current);
    setCurrentImageIndex((prevIndex: number) =>
      prevIndex === images.length - 1 ? 0 : prevIndex + 1,
    );
    startAnimation();
  };

  useEffect(() => {
    if (!figRef.current || normalMode) return;

    const once = inView(figRef.current, () => {
      if (!figRef.current) return;
      const el = figRef.current.querySelector("a > img");
      if (!el) return;

      controlRef.current = animate(
        el,
        {
          opacity: [0, 1],
          x: [-100, 0],
          clipPath: [
            "polygon(0 0, 0 0, 0 100%, 0 100%)",
            "polygon(0 0, 100% 0, 100% 100%, 0 100%)",
          ],
        },
        {
          ease: "easeOut",
          duration: 0.6,
        },
      );

      startAnimation();

      return () => {
        controlRef.current?.complete();
        clearInterval(intervalId.current);
        once();
      };
    });

    return () => {
      controlRef.current?.complete();
      clearInterval(intervalId.current);
      once();
    };
  }, [images, isExpanded]);

  useEffect(() => {
    if (normalMode) return;
    controlRef.current?.play();
  }, [deferredCurrentImageIndex]);

  return (
    <figure
      ref={figRef}
      className={cn("group relative max-h-[80vh] overflow-hidden", className)}
      onClick={handleNextImage}
      onMouseMove={
        !normalMode
          ? () => {
              if (intervalId.current) clearInterval(intervalId.current);
            }
          : undefined
      }
      onMouseLeave={!normalMode ? startAnimation : undefined}
    >
      {!normalMode && prevDisplayedImage && (
        <Image
          src={prevDisplayedImage.src}
          alt={prevDisplayedImage.title}
          width={1080}
          height={720}
          className={cn("absolute inset-0 z-0 h-full w-full object-cover")}
          sizes="50vw"
          loading={normalMode ? "lazy" : "eager"}
        />
      )}

      <Link
        href={src}
        data-pswp-width={width}
        data-pswp-height={height}
        data-pswp-srcset={srcSet}
        target="_blank"
        rel="noreferrer"
        className={cn(
          "h-full w-full",
          isExpanded ? "pointer-events-auto" : "pointer-events-none",
          !normalMode && prevDisplayedImage && "relative z-10",
        )}
      >
        <Image
          src={displayedImage.src}
          alt={displayedImage.title}
          width={1080}
          height={720}
          className={cn(
            "h-full w-full transform-gpu object-cover transition-transform duration-300 ease-out",
            isExpanded ? "group-hover:scale-105" : "",
          )}
          sizes="50vw"
          loading={normalMode ? "lazy" : "eager"}
        />
      </Link>
      <figcaption
        className={cn(
          "absolute inset-x-0 bottom-0 w-full translate-y-full transform-gpu bg-black/80 p-5 transition-transform duration-300",
          isExpanded ? "group-hover:translate-y-0" : "",
        )}
      >
        <h3 className="text-center text-base font-semibold text-white">
          {displayedImage.title}
        </h3>
      </figcaption>
    </figure>
  );
};

const ImageGrid = ({
  images = [],
  className,
  isExpanded = true,
}: ImageGridProps) => {
  return (
    <div
      className={cn(
        "pswp-gallery grid h-auto transition-all duration-500 ease-in-out",
        isExpanded ? "grid-rows-[1fr]" : "grid-rows-[0fr]",
      )}
    >
      <section className={cn("overflow-hidden", className)}>
        <div className="grid grid-cols-2">
          {images.map((image, index) => (
            <ImageGridItem
              key={image.title + index}
              isExpanded={isExpanded}
              image={image}
              className={cn(index % 3 === 2 ? "col-span-2" : "col-span-1")}
            />
          ))}
        </div>
      </section>
    </div>
  );
};

export default ImageGrid;
