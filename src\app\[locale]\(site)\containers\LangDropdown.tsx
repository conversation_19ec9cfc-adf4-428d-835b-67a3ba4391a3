"use client";
import { buttonVariants } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Link } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { ArrowDown } from "lucide-react";
import { usePathname } from "next/navigation";
import { useSlideAnimation } from "../animations";

const LangDropdown = ({ className }: { className?: string }) => {
  const pathName = usePathname();

  const locale = pathName.split("/")[1];

  const { scope } = useSlideAnimation<HTMLButtonElement>({
    delay: 1.5,
    duration: 0.5,
    inViewProps: { margin: "10% 0px" },
    direction: "right",
  });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        ref={scope}
        className={cn(
          buttonVariants({ variant: "default", size: "md" }),
          "flex w-fit items-center gap-x-2 overflow-hidden text-xs uppercase opacity-0 transition-all duration-200 ease-in",
          className,
        )}
      >
        <p className="border-background/50 border-r-2 pr-2">{locale}</p>
        <ArrowDown className="size-4" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Idiomas</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <Link href="/" locale="pt">
          <DropdownMenuItem>Português</DropdownMenuItem>
        </Link>
        <Link href="/" locale="en">
          <DropdownMenuItem>English</DropdownMenuItem>
        </Link>
        <Link href="/" locale="fr">
          <DropdownMenuItem>Français</DropdownMenuItem>
        </Link>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LangDropdown;
