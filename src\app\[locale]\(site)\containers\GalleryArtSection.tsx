"use client";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useSlideAnimation } from "../animations";
import { galleryImages } from "../data";
import ImageGrid, { ImageGridItem } from "../gallery/ImageGrid";
import { frameImages } from "../data-new";
import { useTranslations } from "next-intl";

const GalleryArtSection = ({
  isExpanded,
  onExpand,
}: {
  isExpanded: boolean;
  onExpand: () => void;
}) => {
  const { scope: titleRef } = useSlideAnimation<HTMLSpanElement>();
  const { scope: descRef } = useSlideAnimation<HTMLParagraphElement>({
    delay: 0.5,
  });

  const { scope: buttonRef } = useSlideAnimation<HTMLButtonElement>({
    delay: 1,
    direction: "bottom",
  });

  const t = useTranslations("Gallery");

  return (
    <section>
      <div className="flex px-5 max-sm:flex-col sm:px-10">
        <article className="flex flex-1 flex-col overflow-hidden bg-foreground">
          <span className="relative text-3xl font-bold lowercase text-secondary-foreground lg:text-6xl">
            <span className="absolute -left-20 top-[20%] z-1 h-[60%] w-[150%] rounded-[100%] [box-shadow:0px_20px_15px_15px_rgba(0,0,0,.7)]"></span>
            <h4 className="relative z-1 bg-foreground px-10 py-5">
              <span
                className="inline-block leading-tight opacity-0"
                ref={titleRef}
              >
                {t("title")}
              </span>
            </h4>
          </span>
          <div className="m-auto flex flex-col justify-center gap-8 max-lg:py-8">
            <p
              ref={descRef}
              className={cn(
                "font-bold opacity-0 transition-all duration-300",
                isExpanded ? "text-2xl sm:text-4xl" : "text-lg sm:text-2xl",
              )}
            >
              {t.rich("artSubtitle", {
                line: (chunks) => (
                  <>
                    {chunks}
                    <br />
                  </>
                ),
              })}
            </p>
            <Button
              ref={buttonRef}
              variant="outline"
              className={cn(
                "w-fit transform-gpu border-secondary-foreground opacity-0 transition-all duration-300",
              )}
              onClick={onExpand}
            >
              {isExpanded ? t("close") : t("explore")}
            </Button>
          </div>
        </article>
        <ImageGridItem
          isExpanded={isExpanded}
          image={galleryImages[0]}
          className="aspect-[4/2.5] flex-1 sm:max-w-[50%]"
          images={[galleryImages[0], ...frameImages].slice(0, 5)}
        />
      </div>
      <ImageGrid images={frameImages} isExpanded={isExpanded} />
    </section>
  );
};

export default GalleryArtSection;
