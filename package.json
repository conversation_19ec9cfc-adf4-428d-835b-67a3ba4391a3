{"name": "pedro-yaba", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "sanity:schema:extract": "npx sanity@latest schema extract --enforce-required-fields --path ./src/sanity/schema.json", "sanity:typegen": "npx sanity@latest typegen generate"}, "dependencies": {"@gsap/react": "^2.1.1", "@hookform/resolvers": "^5.2.1", "@next/third-parties": "15.0.3", "@portabletext/to-html": "^3.0.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@sanity/icons": "^3.7.4", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^4.6.0", "@sanity/webhook": "^4.0.4", "@types/nodemailer": "^7.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "embla-carousel-react": "^8.3.1", "gsap": "^3.12.5", "lenis": "^1.1.16", "lucide-react": "^0.456.0", "marked": "^15.0.0", "motion": "^11.11.14", "next": "15.5.2", "next-intl": "^4.3.5", "next-sanity": "^10.0.14", "nodemailer": "^7.0.5", "photoswipe": "^5.4.4", "react": "19.1.1", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "sanity": "^4.6.0", "sonner": "^2.0.7", "styled-components": "^6.1.19", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^4.1.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@types/node": "^24.3.0", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "eslint": "^9.34.0", "eslint-config-next": "15.5.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}, "pnpm": {"overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild", "sharp", "unrs-resolver"]}}