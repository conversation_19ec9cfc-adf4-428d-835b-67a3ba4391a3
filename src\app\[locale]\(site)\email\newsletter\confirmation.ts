import nodemailer from "nodemailer";
import Mail from "nodemailer/lib/mailer";
import { createTransporter, getFromAddress } from "./index";

/**
 * Send confirmation email to new newsletter subscriber
 */
export async function sendConfirmationEmail(
  email: string,
  unsubscribeUrl: string,
) {
  try {
    const transporter = createTransporter();

    const mailOptions: Mail.Options = {
      from: getFromAddress(),
      to: email,
      subject: "Bem-vindo à Newsletter do Pedro Yaba",
      html: `
        <!DOCTYPE html>
        <html lang="pt">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Bem-vindo à Newsletter</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f5f5f5;
            }
            .container {
              background-color: white;
              border-radius: 10px;
              overflow: hidden;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
              background: linear-gradient(135deg, #2a4255 0%, #4f7da1 100%);
              color: white;
              padding: 30px;
              text-align: center;
            }
            .header h1 {
              margin: 0;
              font-size: 24px;
              font-weight: bold;
            }
            .content {
              padding: 30px;
              font-size: 16px;
            }
            .footer {
              background-color: #f9f9f9;
              padding: 20px 30px;
              text-align: center;
              border-top: 1px solid #eee;
              font-size: 14px;
              color: #666;
            }
            .unsubscribe-link {
              color: #666;
              text-decoration: none;
            }
            .unsubscribe-link:hover {
              text-decoration: underline;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Bem-vindo!</h1>
              <p style="margin: 10px 0 0 0; font-size: 14px; opacity: 0.9;">Newsletter do Pedro Yaba</p>
            </div>
            
            <div class="content">
              <p>Olá!</p>
              
              <p>Obrigado por se inscrever na minha newsletter! Estou muito feliz em tê-lo(a) como parte da nossa comunidade.</p>
              
              <p>Através desta newsletter, você receberá:</p>
              <ul>
                <li>Atualizações sobre meus projetos e trabalhos</li>
                <li>Reflexões sobre tecnologia, inovação e empreendedorismo</li>
                <li>Conteúdo exclusivo e insights do mercado angolano</li>
                <li>Oportunidades e eventos relevantes</li>
              </ul>
              
              <p>Prometo enviar apenas conteúdo de qualidade e relevante para você.</p>
              
              <p>Mais uma vez, bem-vindo(a) à nossa comunidade!</p>
              
              <p>Atenciosamente,<br>
              <strong>Pedro Yaba</strong></p>
            </div>
            
            <div class="footer">
              <p>
                Se você não deseja mais receber estes emails, pode 
                <a href="${unsubscribeUrl}" class="unsubscribe-link">cancelar sua inscrição aqui</a>.
              </p>
              <p>Pedro Yaba | Luanda, Angola</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        Bem-vindo à Newsletter do Pedro Yaba!
        
        Olá!
        
        Obrigado por se inscrever na minha newsletter! Estou muito feliz em tê-lo(a) como parte da nossa comunidade.
        
        Através desta newsletter, você receberá:
        - Atualizações sobre meus projetos e trabalhos
        - Reflexões sobre tecnologia, inovação e empreendedorismo
        - Conteúdo exclusivo e insights do mercado angolano
        - Oportunidades e eventos relevantes
        
        Prometo enviar apenas conteúdo de qualidade e relevante para você.
        
        Mais uma vez, bem-vindo(a) à nossa comunidade!
        
        Atenciosamente,
        Pedro Yaba
        
        ---
        
        Se você não deseja mais receber estes emails, pode cancelar sua inscrição aqui: ${unsubscribeUrl}
        
        Pedro Yaba | Luanda, Angola
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Confirmation email sent successfully to:", email);

    if (process.env.NODE_ENV !== "production") {
      console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
    }
  } catch (error) {
    console.error("Error sending confirmation email:", error);
    throw error;
  }
}
