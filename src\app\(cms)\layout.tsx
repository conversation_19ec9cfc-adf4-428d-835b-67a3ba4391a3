import type { <PERSON>ada<PERSON> } from "next";
import "../globals.css";
import { cn } from "@/lib/utils";
import { mainFont } from "../fonts";

export const metadata: Metadata = {
  title: "<PERSON>",
  description: "Pedro Yaba website CMS",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html>
      <body className={cn("", mainFont.variable)}>{children}</body>
    </html>
  );
}
