import nodemailer from "nodemailer";
import Mail from "nodemailer/lib/mailer";
import { createTransporter, getFromAddress } from "./index";
import {
  blockContentToHtml,
  blockContentToText,
} from "@/lib/portableTextToHtml";
import type { Newsletter } from "@/sanity/sanity.types";

/**
 * Send newsletter email to subscriber
 */
export async function sendNewsletterEmail(
  email: string,
  newsletter: Newsletter,
  unsubscribeUrl: string,
) {
  try {
    const transporter = createTransporter();

    // Convert block content to HTML and text
    const htmlContent = blockContentToHtml(newsletter.body);
    const textContent = blockContentToText(newsletter.body);

    const mailOptions: Mail.Options = {
      from: getFromAddress(),
      to: email,
      subject: newsletter.subject,
      html: `
        <!DOCTYPE html>
        <html lang="pt">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${newsletter.subject}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f5f5f5;
            }
            .container {
              background-color: white;
              border-radius: 10px;
              overflow: hidden;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
              background: linear-gradient(135deg, #2a4255 0%, #4f7da1 100%);
              color: white;
              padding: 30px;
              text-align: center;
            }
            .header h1 {
              margin: 0;
              font-size: 24px;
              font-weight: bold;
            }
            .content {
              padding: 30px;
              font-size: 16px;
            }
            .footer {
              background-color: #f9f9f9;
              padding: 20px 30px;
              text-align: center;
              border-top: 1px solid #eee;
              font-size: 14px;
              color: #666;
            }
            .unsubscribe-link {
              color: #666;
              text-decoration: none;
            }
            .unsubscribe-link:hover {
              text-decoration: underline;
            }
            .signature {
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #eee;
              font-style: italic;
              color: #666;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>${newsletter.subject}</h1>
              <p style="margin: 10px 0 0 0; font-size: 14px; opacity: 0.9;">Newsletter do Pedro Yaba</p>
            </div>
            
            <div class="content">
              ${htmlContent}
              
              <div class="signature">
                <p>Atenciosamente,<br>
                <strong>Pedro Yaba</strong></p>
              </div>
            </div>
            
            <div class="footer">
              <p>
                Se você não deseja mais receber estes emails, pode 
                <a href="${unsubscribeUrl}" class="unsubscribe-link">cancelar sua inscrição aqui</a>.
              </p>
              <p>Pedro Yaba | Luanda, Angola</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        ${newsletter.subject}
        Newsletter do Pedro Yaba
        
        ${textContent}
        
        Atenciosamente,
        Pedro Yaba
        
        ---
        
        Se você não deseja mais receber estes emails, pode cancelar sua inscrição aqui: ${unsubscribeUrl}
        
        Pedro Yaba | Luanda, Angola
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Newsletter email sent successfully to:", email);

    if (process.env.NODE_ENV !== "production") {
      console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
    }
  } catch (error) {
    console.error("Error sending newsletter email:", error);
    throw error;
  }
}
