"use client";
import { cn } from "@/lib/utils";
import { useRef, useEffect, useState } from "react";
import { inView } from "motion";

type VideoGridProps = {
  className?: string;
  expanded?: boolean;
};

export const VideoGridItem = ({
  className,
  expanded,
}: {
  expanded: boolean;
  className?: string;
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    if (!videoRef.current) return;

    // Stop video when expanded
    if (expanded) {
      videoRef.current.pause();
      setIsPlaying(false);
      return;
    }

    const unsubscribe = inView(videoRef.current, () => {
      if (!expanded) {
        videoRef.current?.play();
        setIsPlaying(true);
      }
      return () => {
        videoRef.current?.pause();
        setIsPlaying(false);
      };
    });

    return () => {
      unsubscribe();
    };
  }, [expanded]);

  const handleVideoClick = () => {
    if (!videoRef.current || expanded) return;

    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  return (
    <div
      className={cn(
        "relative overflow-hidden transition-all duration-500",
        className,
      )}
    >
      <video
        ref={videoRef}
        src="/vids/about.mp4"
        className={cn(
          "h-full w-full cursor-pointer object-cover",
          expanded && "cursor-default",
        )}
        muted
        loop
        playsInline
        onClick={handleVideoClick}
      />
    </div>
  );
};

const VideoGrid = ({ className, expanded = true }: VideoGridProps) => {
  return (
    <div
      className={cn(
        "grid h-auto overflow-hidden px-5 transition-all duration-500 ease-in-out sm:px-10",
        expanded ? "grid-rows-[1fr]" : "grid-rows-[0fr]",
      )}
    >
      <section className={cn("overflow-hidden", className)}>
        <div className="grid grid-cols-2">
          <video
            src="/vids/about.mp4"
            className="h-full max-h-[80vh] bg-black w-full object-contain col-span-2"
            controls
            playsInline
          />
        </div>
      </section>
    </div>
  );
};

export default VideoGrid;
