import { getTranslations } from "next-intl/server";

export type LinkType = {
  label: string;
  href: string;
  description: string;
};

type LinkTranslationType = Awaited<ReturnType<typeof getTranslations<"Links">>>;

export const getLinks = (t: LinkTranslationType) => {
  return {
    about: {
      label: t("about.label"),
      href: t("about.href"),
      description: t("about.description"),
    },
    gallery: {
      label: t("gallery.label"),
      href: t("gallery.href"),
      description: t("gallery.description"),
    },
    press: {
      label: t("press.label"),
      href: t("press.href"),
      description: t("press.description"),
    },
    contact: {
      label: t("contact.label"),
      href: t("contact.href"),
      description: t("contact.description"),
    },
  };
};

export const linksObj = {
  home: { label: "Início", href: "#" },
  about: { label: "Sobre Mim", href: "#about" },
  gallery: {
    label: "Galeria",
    href: "#gallery",
  },
  press: { label: "I<PERSON><PERSON><PERSON>", href: "#press" },
  contact: {
    label: "Contacto",
    href: "#contact",
  },
  studio: { label: "Studio", href: "/studio" },
};

export const navLinks = [
  linksObj.about,
  linksObj.gallery,
  linksObj.press,
  linksObj.contact,
];
