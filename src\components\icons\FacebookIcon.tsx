import { cn } from "@/lib/utils";
import React, { SVGProps } from "react";

type Props = SVGProps<SVGSVGElement> & {
  innerPathClassName?: string;
};

const FacebookIcon = ({ innerPathClassName = "", ...props }: Props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M18.7013 24.0002H5.29869C2.37231 24.0002 0 21.628 0 18.7016V5.29894C0 2.37256 2.37231 0.000244141 5.29869 0.000244141H18.7013C21.6277 0.000244141 24 2.37256 24 5.29894V18.7016C24 21.628 21.6277 24.0002 18.7013 24.0002Z"
      className="fill-current"
    />
    <path
      d="M16.7908 7.84684L18.6966 7.89639V4.42794L15.8723 4.32886C13.2179 4.32886 11.0661 6.48063 11.0661 9.13501V12.2071H7.7959V15.9233H11.0661V24.0002H15.1787V15.9233H18.102L18.6966 12.2071H15.1787V9.45892C15.1787 8.56859 15.9004 7.84684 16.7908 7.84684Z"
      className={cn("fill-background", innerPathClassName)}
    />
  </svg>
);

export default FacebookIcon;
