import localFont from "next/font/local";
// src\app\fonts\gothampro_bold.ttf
// src\app\fonts\gothampro_medium.ttf
// src\app\fonts\gothampro.ttf

export const mainFont = localFont({
  variable: "--font-sans",
  display: "swap",
  preload: true,
  src: [
    {
      path: "./gothampro_light.ttf",
      weight: "300",
    },
    {
      path: "./gothampro.ttf",
      weight: "400",
    },
    {
      path: "./gothampro_medium.ttf",
      weight: "500",
    },
    {
      path: "./gothampro_bold.ttf",
      weight: "700",
    },
  ],
});
