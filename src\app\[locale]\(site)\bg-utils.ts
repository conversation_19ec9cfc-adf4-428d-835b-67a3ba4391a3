import PedroImage from "@/assets/imgs/pedro.jpg";

import { getImageProps } from "next/image";
import { preload } from "react-dom";

function getBackgroundImage(srcSet = "") {
  const imageSet = srcSet
    .split(", ")
    .map((str) => {
      const [url, dpi] = str.split(" ");
      return `url("${url}") ${dpi}`;
    })
    .join(", ");
  return `image-set(${imageSet})`;
}

const {
  props: { srcSet },
} = getImageProps({
  alt: "Pedro Yaba",
  width: 1080,
  height: 1920,
  src: PedroImage,
  loading: "eager",
});

const backgroundImage = getBackgroundImage(srcSet);
srcSet && preload(srcSet, { as: "image" });

const pedroBgStyles = {
  backgroundImage,
  backgroundRepeat: "no-repeat",
  backgroundSize: "cover",
  backgroundPositionY: "25%",
  backgroundAttachment: "fixed",
};

export { pedroBgStyles };
