import { cn } from "@/lib/utils";
import React, { SVGProps } from "react";

type Props = SVGProps<SVGSVGElement> & {
  innerPathClassName?: string;
};

const YoutubeIcon = ({ innerPathClassName = "", ...props }: Props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M18.5087 0.000610352H5.49129C2.45887 0.000610352 0 2.45916 0 5.49192V18.5091C0 21.5418 2.45887 24.0003 5.49129 24.0003H18.5087C21.5416 24.0003 24 21.5418 24 18.5091V5.49192C24 2.45916 21.5416 0.000610352 18.5087 0.000610352Z"
      className="fill-current"
    />
    <path
      d="M20.9804 11.919C20.9804 11.919 21.1437 8.00412 20.1819 6.77955C19.5578 5.98441 18.4291 5.81317 17.1168 5.77131C14.7923 5.69712 12.1885 5.69625 11.9825 5.69629C11.7759 5.69625 9.17263 5.69712 6.84807 5.77131C5.53538 5.81317 4.40715 5.98441 3.78253 6.77955C2.82124 8.00412 2.98406 11.919 2.98406 11.919C2.98406 11.919 2.82124 15.834 3.78253 17.0586C4.40668 17.8537 5.53538 18.025 6.84761 18.0668C9.17263 18.141 11.7759 18.1418 11.9825 18.1418C12.1885 18.1418 14.7923 18.141 17.1168 18.0668C18.4291 18.025 19.5578 17.8537 20.1819 17.0586C21.1437 15.834 20.9804 11.919 20.9804 11.919Z"
      className={cn("fill-background", innerPathClassName)}
    />
    <path
      d="M10.2041 9.28613V14.6471L14.8864 11.9666L10.2041 9.28613Z"
      className="fill-current"
    />
  </svg>
);

export default YoutubeIcon;
